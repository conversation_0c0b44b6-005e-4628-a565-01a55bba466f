"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Button, ButtonProps } from "@/components/ui/button";
import { Section } from "@/components/landing/section";

interface HeroAction extends ButtonProps {
  href: string;
  label: string;
}

interface HeroProps {
  title: React.ReactNode;
  subtitle?: React.ReactNode;
  image?: {
    src: string;
    alt: string;
    width: number;
    height: number;
  };
  align?: "left" | "center" | "right";
  primaryAction?: HeroAction;
  secondaryAction?: HeroAction;
  className?: string;
  children?: React.ReactNode;
}

export function Hero({
  title,
  subtitle,
  image,
  align = "center",
  primaryAction,
  secondaryAction,
  className,
  children,
}: HeroProps) {
  return (
    <Section 
      className={cn("xl:py-48", className)}
    >
      <div 
        className={cn(
          "flex flex-col space-y-8",
          align === "center" && "items-center text-center",
          align === "right" && "items-end text-right",
          image && "lg:flex-row lg:space-y-0 lg:space-x-8"
        )}
      >
        <div className={cn(
          "flex flex-col space-y-4",
          image && "lg:w-1/2"
        )}>
          <div className="space-y-2">
            {typeof title === "string" ? (
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                {title}
              </h1>
            ) : (
              title
            )}
            
            {subtitle && typeof subtitle === "string" ? (
              <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                {subtitle}
              </p>
            ) : (
              subtitle
            )}
          </div>
          
          {(primaryAction || secondaryAction) && (
            <div className="flex flex-wrap gap-4">
              {primaryAction && (
                <Link href={primaryAction.href}>
                  <Button size="lg" {...primaryAction}>
                    {primaryAction.label}
                  </Button>
                </Link>
              )}
              
              {secondaryAction && (
                <Link href={secondaryAction.href}>
                  <Button 
                    variant="outline" 
                    size="lg" 
                    {...secondaryAction}
                  >
                    {secondaryAction.label}
                  </Button>
                </Link>
              )}
            </div>
          )}
          
          {children}
        </div>
        
        {image && (
          <div className="lg:w-1/2">
            <Image
              src={image.src}
              alt={image.alt}
              width={image.width}
              height={image.height}
              className="w-full h-auto object-cover rounded-lg"
            />
          </div>
        )}
      </div>
    </Section>
  );
}

// Default implementation for backward compatibility
export function LandingHero() {
  return (
    <Hero
      title="The Ultimate SAAS Platform for Your Business"
      subtitle="Streamline your workflow, collaborate with your team, and grow your business with our all-in-one solution."
      primaryAction={{
        href: "/auth/register",
        label: "Get Started",
      }}
      secondaryAction={{
        href: "/pricing",
        label: "View Pricing",
      }}
    />
  );
}