"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { Section, SectionHeader, SectionContent } from "@/components/landing/section";
import {
  BarChart3,
  Clock,
  CreditCard,
  Globe,
  LayoutDashboard,
  Settings,
  Shield,
  Users,
  LucideIcon,
} from "lucide-react";

interface Feature {
  icon?: React.ReactNode;
  title: string;
  description?: string;
}

interface FeaturesProps {
  title?: string;
  description?: string;
  badge?: string;
  features: Feature[];
  columns?: 1 | 2 | 3 | 4;
  className?: string;
  background?: "default" | "muted" | "primary" | "secondary";
  align?: "left" | "center" | "right";
  iconPosition?: "top" | "left";
  bordered?: boolean;
  children?: React.ReactNode;
}

export function Features({
  title = "Features",
  description,
  badge,
  features,
  columns = 3,
  className,
  background = "default",
  align = "center",
  iconPosition = "top",
  bordered = true,
  children,
}: FeaturesProps) {
  const backgroundClasses = {
    default: "",
    muted: "bg-muted/50",
    primary: "bg-primary text-primary-foreground",
    secondary: "bg-secondary",
  };

  return (
    <Section className={cn(backgroundClasses[background], className)}>
      {(title || description || badge) && (
        <SectionHeader
          title={title}
          description={description}
          badge={badge}
          align={align}
        />
      )}
      
      <SectionContent>
        <div 
          className={cn(
            "mx-auto grid max-w-6xl gap-6 pt-8 md:pt-12",
            columns === 1 && "grid-cols-1",
            columns === 2 && "grid-cols-1 md:grid-cols-2",
            columns === 3 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
            columns === 4 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
          )}
        >
          {features.map((feature, index) => (
            <div
              key={index}
              className={cn(
                "flex flex-col space-y-2 p-4",
                bordered && "rounded-lg border",
                iconPosition === "left" && "flex-row items-start space-x-4 space-y-0",
                align === "center" && iconPosition === "top" && "items-center text-center",
              )}
            >
              {feature.icon && (
                <div className={cn(
                  "rounded-full border p-2",
                  iconPosition === "left" && "mt-1",
                )}>
                  {feature.icon}
                </div>
              )}
              <div className={cn(
                "space-y-2",
                iconPosition === "left" && "flex-1",
              )}>
                <h3 className="text-xl font-bold">{feature.title}</h3>
                {feature.description && (
                  <p className={cn(
                    "text-sm text-muted-foreground",
                    align === "center" && iconPosition === "top" && "text-center",
                  )}>
                    {feature.description}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {children}
      </SectionContent>
    </Section>
  );
}

// Default implementation for backward compatibility
export function LandingFeatures() {
  const defaultFeatures = [
    {
      icon: <LayoutDashboard className="h-6 w-6" />,
      title: "Intuitive Dashboard",
      description:
        "Get a clear overview of your business with our intuitive dashboard.",
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: "Team Collaboration",
      description:
        "Work together seamlessly with your team members in real-time.",
    },
    {
      icon: <BarChart3 className="h-6 w-6" />,
      title: "Advanced Analytics",
      description:
        "Make data-driven decisions with our powerful analytics tools.",
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: "Time Tracking",
      description:
        "Track time spent on projects and tasks to improve productivity.",
    },
    {
      icon: <CreditCard className="h-6 w-6" />,
      title: "Secure Payments",
      description:
        "Process payments securely with our integrated payment system.",
    },
    {
      icon: <Settings className="h-6 w-6" />,
      title: "Customizable",
      description:
        "Tailor the platform to your specific needs with customizable settings.",
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: "Enterprise Security",
      description:
        "Keep your data safe with our enterprise-grade security features.",
    },
    {
      icon: <Globe className="h-6 w-6" />,
      title: "Global Support",
      description:
        "Get help whenever you need it with our 24/7 global support team.",
    },
  ];

  return (
    <Features
      title="Everything you need to succeed"
      description="Our platform provides all the tools you need to manage your business efficiently."
      badge="Features"
      features={defaultFeatures}
      columns={4}
      background="muted"
    />
  );
}